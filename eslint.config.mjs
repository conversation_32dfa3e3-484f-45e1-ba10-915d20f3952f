import js from '@eslint/js'
import ts from 'typescript-eslint'

export default [
  // ignore non-source stuff
  {
    ignores: ['node_modules', 'dist', 'coverage', '.env', 'eslint.config.mjs']
  },

  js.configs.recommended,

  // base TS rules (no type-check)
  ...ts.configs.recommended,

  // enable type-checked rules ONLY for TS files
  ...ts.configs.recommendedTypeChecked,
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.json'], // or switch to tsconfig.eslint.json below
        tsconfigRootDir: import.meta.dirname
      }
    },
    rules: {
      // Enhanced error handling rules
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/no-unsafe-assignment': 'error',
      '@typescript-eslint/no-unsafe-member-access': 'error',
      '@typescript-eslint/no-unsafe-call': 'error',
      '@typescript-eslint/no-unsafe-return': 'error',
      '@typescript-eslint/no-unsafe-argument': 'error',
      '@typescript-eslint/prefer-promise-reject-errors': 'error',
      '@typescript-eslint/use-unknown-in-catch-callback-variable': 'error',

      // Allow unused parameters that start with underscore
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_'
        }
      ],

      // Require proper error handling
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/await-thenable': 'error',
      '@typescript-eslint/no-misused-promises': 'error',

      // Prevent unsafe operations
      '@typescript-eslint/restrict-template-expressions': 'error',
      '@typescript-eslint/restrict-plus-operands': 'error'
    }
  }
]
