import mongoose, { type Document, Schema, Types, type Model } from 'mongoose'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { env } from '../utils/env'
import { ConfigurationError, DatabaseError } from '../common/errors'
import { validateEmail } from '../utils/helpers'

// -----------------------------------------------
// Constants and Shared Configuration
// -----------------------------------------------

/** Common schema options used across all models */
const SCHEMA_OPTIONS = {
  timestamps: true,
  versionKey: false
} as const

/** JWT token configuration */
const JWT_CONFIG = {
  expiresIn: '1h',
  issuer: 'node-backend',
  audience: 'client-app'
} as const

/** Password validation constants */
const PASSWORD_CONSTRAINTS = {
  MIN_LENGTH: 8,
  SALT_ROUNDS: 12
} as const

// -----------------------------------------------
// Base Interfaces
// -----------------------------------------------

/**
 * Base user properties shared across interfaces
 */
interface BaseUserProperties {
  /** User's first name */
  firstName: string
  /** User's last name (optional) */
  lastName?: string
  /** User's email address (unique) */
  email: string
  /** Array of role references assigned to the user */
  roles: Types.Array<Types.ObjectId>
}

/**
 * User document interface for MongoDB operations
 * Represents a user with authentication and role management capabilities
 */
export interface IUser extends Document, BaseUserProperties {
  /** Reference to the user's password document */
  password: Types.ObjectId

  // Methods
  /** Generates a valid JWT authentication token */
  generateAuthToken(): string
  /** Generates a fake token for security purposes */
  generateFakeAuthToken(): string
}

/**
 * User data interface for API operations
 * Used for data transfer and validation
 */
export interface IUserData extends Document, BaseUserProperties {
  /** Plain text password (for input validation only) */
  password: string
}

/**
 * User role interface
 * Defines permissions and access levels
 */
export interface IUserRole extends Document {
  /** Unique role name */
  name: string
  /** Array of permission strings */
  permissions: string[]
}

/**
 * Password document interface
 * Handles secure password storage and validation
 */
export interface IPassword extends Document {
  /** Bcrypt hashed password */
  hash: string
  /** Reference to the user this password belongs to */
  user: Types.ObjectId

  // Methods
  /** Validates a plain text password against the stored hash */
  checkPassword(password: string): Promise<boolean>
}

// -----------------------------------------------
// Utility Functions
// -----------------------------------------------

/**
 * Creates a model with consistent export pattern
 * @param modelName - Name of the model
 * @param schema - Mongoose schema
 * @returns Model instance
 */
const createModel = <T extends Document>(
  modelName: string,
  schema: Schema<T>
): Model<T> => {
  return (
    (mongoose.models[modelName] as Model<T>) ||
    mongoose.model<T>(modelName, schema)
  )
}

/**
 * Validates that all permissions are non-empty strings
 * @param permissions - Array of permission strings
 * @returns True if all permissions are valid
 */
const validatePermissions = (permissions: string[]): boolean => {
  return permissions.every(
    (permission) =>
      typeof permission === 'string' && permission.trim().length > 0
  )
}

// -----------------------------------------------
// Schema Definitions
// -----------------------------------------------

/**
 * Role schema definition
 * Defines user roles and their associated permissions
 */
const roleSchema: Schema<IUserRole> = new Schema<IUserRole>(
  {
    name: {
      type: String,
      required: [true, 'Role name is required'],
      unique: true,
      trim: true,
      minlength: [2, 'Role name must be at least 2 characters long'],
      maxlength: [50, 'Role name cannot exceed 50 characters']
    },
    permissions: {
      type: [String],
      default: [],
      validate: {
        validator: validatePermissions,
        message: 'All permissions must be non-empty strings'
      }
    }
  },
  SCHEMA_OPTIONS
)

/**
 * User schema definition
 * Main user document with authentication and role references
 */
const userSchema: Schema<IUser> = new Schema<IUser>(
  {
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true,
      minlength: [1, 'First name must be at least 1 character long'],
      maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
      type: String,
      required: false,
      trim: true,
      maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      validate: {
        validator: validateEmail,
        message: 'Please provide a valid email address'
      }
    },
    password: {
      type: Schema.Types.ObjectId,
      ref: 'Password',
      required: [true, 'Password reference is required']
    },
    roles: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Role'
      }
    ]
  },
  SCHEMA_OPTIONS
)

/**
 * Password schema definition
 * Handles secure password storage with bcrypt hashing
 */
const passwordSchema: Schema<IPassword> = new Schema<IPassword>(
  {
    hash: {
      type: String,
      required: [true, 'Password hash is required'],
      trim: true,
      minlength: [
        PASSWORD_CONSTRAINTS.MIN_LENGTH,
        `Password must be at least ${PASSWORD_CONSTRAINTS.MIN_LENGTH} characters long`
      ]
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required'],
      unique: true
    }
  },
  SCHEMA_OPTIONS
)

// -----------------------------------------------
// Schema Middleware and Methods
// -----------------------------------------------

/**
 * Pre-save middleware for password hashing
 * Automatically hashes the password before saving to database
 * Note: Password length validation is handled by schema validation
 */
passwordSchema.pre<IPassword>('save', async function (next) {
  // Only hash if password has been modified
  if (!this.isModified('hash')) {
    return next()
  }

  try {
    const salt = await bcrypt.genSalt(PASSWORD_CONSTRAINTS.SALT_ROUNDS)
    this.hash = await bcrypt.hash(this.hash, salt)
    next()
  } catch (error: unknown) {
    // Transform bcrypt errors to database errors
    const dbError = new DatabaseError('Failed to hash password', 500, {
      cause: error
    })
    next(dbError)
  }
})

/**
 * Instance method to check password validity
 * @param password - Plain text password to verify
 * @returns Promise resolving to boolean indicating if password is correct
 */
passwordSchema.methods.checkPassword = async function (
  password: string
): Promise<boolean> {
  try {
    if (typeof password !== 'string' || !password) {
      return false
    }
    return await bcrypt.compare(password, this.hash as string)
  } catch (error: unknown) {
    // Log error but don't expose internal details
    console.error('Password comparison failed:', error)
    return false
  }
}

/**
 * Generates a valid JWT authentication token for the user
 * @returns JWT token string
 * @throws ConfigurationError if JWT_SECRET_KEY is not configured
 */
userSchema.methods.generateAuthToken = function (): string {
  const secret = env('JWT_SECRET_KEY')
  if (!secret) {
    throw new ConfigurationError(
      'JWT_SECRET_KEY environment variable is not set'
    )
  }

  try {
    const userId = this._id ? (this._id as Types.ObjectId).toString() : ''
    const token = jwt.sign({ _id: userId }, secret, JWT_CONFIG)
    return token
  } catch (error: unknown) {
    throw new ConfigurationError(
      'Failed to generate authentication token',
      500,
      {
        cause: error
      }
    )
  }
}

/**
 * Generates a fake authentication token for security purposes
 * Used to prevent timing attacks during authentication failures
 * @returns Fake JWT token string
 */
userSchema.methods.generateFakeAuthToken = function (): string {
  const fakeSecret = process.env.FAKE_JWT_SECRET || 'fake-secret-for-security'

  try {
    const token = jwt.sign({ _id: 'fake-id' }, fakeSecret, JWT_CONFIG)
    return token
  } catch {
    // Fallback to a static fake token if JWT generation fails
    return 'fake.jwt.token'
  }
}

// -----------------------------------------------
// Model Creation and Export
// -----------------------------------------------

/**
 * User model - Main user document with authentication capabilities
 */
const User = createModel<IUser>('User', userSchema)

/**
 * Role model - Defines user roles and permissions
 */
export const Role = createModel<IUserRole>('Role', roleSchema)

/**
 * Password model - Handles secure password storage
 */
export const Password = createModel<IPassword>('Password', passwordSchema)

// Default export
export default User
