import { type Request, type Response, type NextFunction } from 'express'

import {
  AppError,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  ResourceError,
  ConfigurationError
} from '../common/errors'
import {
  transformError,
  transformMongoError,
  isAppError
} from '../common/errors/errorUtils'

/**
 * Type guard for MongoDB errors
 */
const isMongoError = (obj: unknown): obj is { code: number } => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'code' in obj &&
    typeof (obj as { code: unknown }).code === 'number'
  )
}

/**
 * Express error handling middleware
 */
const errorMiddleware = (
  err: unknown,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Transform to AppError
  const error: AppError = isAppError(err)
    ? err
    : isMongoError(err)
    ? transformMongoError(err)
    : transformError(err)

  // Development logging
  if (process.env.NODE_ENV === 'development') {
    console.error(`[${new Date().toISOString()}] ${error.name}:`, {
      message: error.message,
      statusCode: error.statusCode,
      url: req.url,
      method: req.method,
      stack: error.stack
    })
  }

  // Handle specific error types
  if (error instanceof DatabaseConnectionError) {
    return res.error('Database connection failed. Please try again later.', 503)
  }

  if (error instanceof DatabaseDuplicateError) {
    return res.error(error.message, 409)
  }

  if (
    error instanceof DatabaseValidationError ||
    error instanceof ValidationError
  ) {
    return res.response(
      { errors: { message: [error.message] } },
      error.statusCode
    )
  }

  if (error instanceof DatabaseError) {
    return res.error('Database operation failed. Please try again later.', 500)
  }

  if (
    error instanceof AuthenticationError ||
    error instanceof AuthorizationError ||
    error instanceof ResourceError
  ) {
    return res.error(error.message, error.statusCode)
  }

  if (error instanceof ConfigurationError) {
    return res.error('Server configuration error. Please contact support.', 500)
  }

  // Default response for all other errors
  res.error(error.message, error.statusCode)
}

export default errorMiddleware
